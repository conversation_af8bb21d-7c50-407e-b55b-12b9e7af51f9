idf_component_register(
    SRCS
        "main.c"
        "base_main.c"
        "ws2812_driver.c"
        "sound_sensor.c"
        "button_handler.c"
        "ambient_effects.c"
        # "bluetooth_comm.c"  # Temporarily disabled due to BT config issues
        # "ble_service.c"     # Temporarily disabled due to BT config issues
        "system_test.c"
    INCLUDE_DIRS
        "."
        "include"
    REQUIRES
        driver
        esp_driver_spi
        nvs_flash
        esp_timer
        esp_adc
        esp_event
        freertos
        log
        spi_flash
        # bt  # Temporarily disabled
)
