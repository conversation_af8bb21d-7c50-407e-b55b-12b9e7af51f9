/**
 * @file bluetooth_comm.c
 * @brief 蓝牙通信模块实现
 * @version 1.0.0
 * @date 2025-06-27
 */

#include "base_config.h"
#include "base_main.h"
#include "esp_log.h"
#include "esp_err.h"
#include "esp_bt.h"
#include "esp_bt_main.h"
#include "esp_gap_ble_api.h"
#include "esp_gatts_api.h"
#include "esp_gatt_common_api.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include <string.h>

static const char *TAG = "BT_COMM";

/* 蓝牙状态 */
static bool g_bluetooth_initialized = false;
static bool g_bluetooth_running = false;
static bool g_bluetooth_connected = false;
static uint16_t g_conn_id = 0;
static esp_gatt_if_t g_gatts_if = ESP_GATT_IF_NONE;

/* 服务和特征值句柄 */
static uint16_t g_service_handle = 0;
static uint16_t g_char_handle_cmd = 0;
static uint16_t g_char_handle_data = 0;

/* UUID定义 */
static uint8_t service_uuid[16] = {
    0xbc, 0x9a, 0x78, 0x56, 0x34, 0x12, 0x34, 0x12, 0x34, 0x12, 0x78, 0x56, 0x34, 0x12, 0x78, 0x56
};

static uint8_t char_uuid_cmd[16] = {
    0x21, 0x43, 0x65, 0x87, 0xa9, 0xcb, 0x21, 0x43, 0x21, 0x43, 0x21, 0x43, 0x65, 0x87, 0xa9, 0xcb
};

static uint8_t char_uuid_data[16] = {
    0x55, 0x55, 0x55, 0x55, 0x44, 0x44, 0x33, 0x33, 0x22, 0x22, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11
};

/* 广播数据 */
static esp_ble_adv_data_t g_adv_data = {
    .set_scan_rsp = false,
    .include_name = true,
    .include_txpower = false,
    .min_interval = 0x0006,
    .max_interval = 0x0010,
    .appearance = 0x00,
    .manufacturer_len = 0,
    .p_manufacturer_data = NULL,
    .service_data_len = 0,
    .p_service_data = NULL,
    .service_uuid_len = sizeof(service_uuid),
    .p_service_uuid = service_uuid,
    .flag = (ESP_BLE_ADV_FLAG_GEN_DISC | ESP_BLE_ADV_FLAG_BREDR_NOT_SPT),
};

/* 广播参数 */
static esp_ble_adv_params_t g_adv_params = {
    .adv_int_min = 0x20,
    .adv_int_max = 0x40,
    .adv_type = ADV_TYPE_IND,
    .own_addr_type = BLE_ADDR_TYPE_PUBLIC,
    .channel_map = ADV_CHNL_ALL,
    .adv_filter_policy = ADV_FILTER_ALLOW_SCAN_ANY_CON_ANY,
};

/**
 * @brief GAP事件处理函数
 */
static void gap_event_handler(esp_gap_ble_cb_event_t event, esp_ble_gap_cb_param_t *param)
{
    switch (event) {
        case ESP_GAP_BLE_ADV_DATA_SET_COMPLETE_EVT:
            ESP_LOGI(TAG, "广播数据设置完成");
            esp_ble_gap_start_advertising(&g_adv_params);
            break;
            
        case ESP_GAP_BLE_ADV_START_COMPLETE_EVT:
            if (param->adv_start_cmpl.status != ESP_BT_STATUS_SUCCESS) {
                ESP_LOGE(TAG, "广播启动失败");
            } else {
                ESP_LOGI(TAG, "广播启动成功");
            }
            break;
            
        case ESP_GAP_BLE_ADV_STOP_COMPLETE_EVT:
            if (param->adv_stop_cmpl.status != ESP_BT_STATUS_SUCCESS) {
                ESP_LOGE(TAG, "广播停止失败");
            } else {
                ESP_LOGI(TAG, "广播停止成功");
            }
            break;
            
        default:
            break;
    }
}

/**
 * @brief GATTS事件处理函数
 */
static void gatts_event_handler(esp_gatts_cb_event_t event, esp_gatt_if_t gatts_if, esp_ble_gatts_cb_param_t *param)
{
    switch (event) {
        case ESP_GATTS_REG_EVT:
            ESP_LOGI(TAG, "GATTS注册完成");
            g_gatts_if = gatts_if;
            
            // 设置设备名称
            esp_ble_gap_set_device_name(BT_DEVICE_NAME);
            
            // 配置广播数据
            esp_ble_gap_config_adv_data(&g_adv_data);
            
            // 创建服务
            esp_ble_gatts_create_service(gatts_if, (esp_gatt_srvc_id_t*)&service_uuid, 4);
            break;
            
        case ESP_GATTS_CREATE_EVT:
            ESP_LOGI(TAG, "服务创建完成，句柄: %d", param->create.service_handle);
            g_service_handle = param->create.service_handle;
            
            // 启动服务
            esp_ble_gatts_start_service(g_service_handle);
            
            // 添加特征值
            esp_bt_uuid_t char_uuid;
            char_uuid.len = ESP_UUID_LEN_128;
            memcpy(char_uuid.uuid.uuid128, char_uuid_cmd, 16);
            
            esp_ble_gatts_add_char(g_service_handle, &char_uuid,
                                   ESP_GATT_PERM_READ | ESP_GATT_PERM_WRITE,
                                   ESP_GATT_CHAR_PROP_BIT_READ | ESP_GATT_CHAR_PROP_BIT_WRITE | ESP_GATT_CHAR_PROP_BIT_NOTIFY,
                                   NULL, NULL);
            break;
            
        case ESP_GATTS_ADD_CHAR_EVT:
            ESP_LOGI(TAG, "特征值添加完成，句柄: %d", param->add_char.attr_handle);
            if (g_char_handle_cmd == 0) {
                g_char_handle_cmd = param->add_char.attr_handle;
                
                // 添加第二个特征值
                esp_bt_uuid_t char_uuid;
                char_uuid.len = ESP_UUID_LEN_128;
                memcpy(char_uuid.uuid.uuid128, char_uuid_data, 16);
                
                esp_ble_gatts_add_char(g_service_handle, &char_uuid,
                                       ESP_GATT_PERM_READ | ESP_GATT_PERM_WRITE,
                                       ESP_GATT_CHAR_PROP_BIT_READ | ESP_GATT_CHAR_PROP_BIT_WRITE | ESP_GATT_CHAR_PROP_BIT_NOTIFY,
                                       NULL, NULL);
            } else {
                g_char_handle_data = param->add_char.attr_handle;
            }
            break;
            
        case ESP_GATTS_CONNECT_EVT:
            ESP_LOGI(TAG, "设备连接，连接ID: %d", param->connect.conn_id);
            g_conn_id = param->connect.conn_id;
            g_bluetooth_connected = true;
            
            // 发送连接事件
            xEventGroupSetBits(g_base_event_group, BASE_EVENT_BLE_CONNECTED);
            break;
            
        case ESP_GATTS_DISCONNECT_EVT:
            ESP_LOGI(TAG, "设备断开连接");
            g_bluetooth_connected = false;
            
            // 发送断开连接事件
            xEventGroupSetBits(g_base_event_group, BASE_EVENT_BLE_DISCONNECTED);
            
            // 重新开始广播
            esp_ble_gap_start_advertising(&g_adv_params);
            break;
            
        case ESP_GATTS_WRITE_EVT:
            ESP_LOGI(TAG, "收到写入数据，长度: %d", param->write.len);
            
            // 处理接收到的数据
            if (param->write.handle == g_char_handle_cmd) {
                // 命令数据
                bluetooth_data_t bt_data = {0};
                bt_data.type = 0x01; // 命令类型
                bt_data.length = param->write.len;
                if (bt_data.length > sizeof(bt_data.data)) {
                    bt_data.length = sizeof(bt_data.data);
                }
                memcpy(bt_data.data, param->write.value, bt_data.length);
                
                // 发送到处理队列
                xQueueSend(g_bluetooth_data_queue, &bt_data, 0);
                
            } else if (param->write.handle == g_char_handle_data) {
                // 数据
                bluetooth_data_t bt_data = {0};
                bt_data.type = 0x02; // 数据类型
                bt_data.length = param->write.len;
                if (bt_data.length > sizeof(bt_data.data)) {
                    bt_data.length = sizeof(bt_data.data);
                }
                memcpy(bt_data.data, param->write.value, bt_data.length);
                
                // 发送到处理队列
                xQueueSend(g_bluetooth_data_queue, &bt_data, 0);
            }
            
            // 发送响应
            if (param->write.need_rsp) {
                esp_ble_gatts_send_response(gatts_if, param->write.conn_id, param->write.trans_id, ESP_GATT_OK, NULL);
            }
            break;
            
        case ESP_GATTS_READ_EVT:
            ESP_LOGI(TAG, "收到读取请求");
            
            // 发送响应数据
            esp_gatt_rsp_t rsp = {0};
            rsp.attr_value.handle = param->read.handle;
            rsp.attr_value.len = 4;
            rsp.attr_value.value[0] = 0x01;
            rsp.attr_value.value[1] = 0x02;
            rsp.attr_value.value[2] = 0x03;
            rsp.attr_value.value[3] = 0x04;
            
            esp_ble_gatts_send_response(gatts_if, param->read.conn_id, param->read.trans_id, ESP_GATT_OK, &rsp);
            break;
            
        default:
            break;
    }
}

/**
 * @brief 初始化蓝牙通信
 */
esp_err_t bluetooth_comm_init(void)
{
    if (g_bluetooth_initialized) {
        ESP_LOGW(TAG, "蓝牙通信已初始化");
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "初始化蓝牙通信...");
    
    // 注册GAP回调
    esp_err_t ret = esp_ble_gap_register_callback(gap_event_handler);
    if (ret) {
        ESP_LOGE(TAG, "注册GAP回调失败: %s", esp_err_to_name(ret));
        return ret;
    }
    
    // 注册GATTS回调
    ret = esp_ble_gatts_register_callback(gatts_event_handler);
    if (ret) {
        ESP_LOGE(TAG, "注册GATTS回调失败: %s", esp_err_to_name(ret));
        return ret;
    }
    
    // 注册GATTS应用
    ret = esp_ble_gatts_app_register(0);
    if (ret) {
        ESP_LOGE(TAG, "注册GATTS应用失败: %s", esp_err_to_name(ret));
        return ret;
    }
    
    g_bluetooth_initialized = true;
    ESP_LOGI(TAG, "蓝牙通信初始化完成");
    return ESP_OK;
}

/**
 * @brief 启动蓝牙通信
 */
esp_err_t bluetooth_comm_start(void)
{
    if (!g_bluetooth_initialized) {
        ESP_LOGE(TAG, "蓝牙通信未初始化");
        return ESP_ERR_INVALID_STATE;
    }
    
    g_bluetooth_running = true;
    ESP_LOGI(TAG, "蓝牙通信启动");
    return ESP_OK;
}

/**
 * @brief 停止蓝牙通信
 */
esp_err_t bluetooth_comm_stop(void)
{
    g_bluetooth_running = false;
    
    if (g_bluetooth_connected) {
        esp_ble_gatts_close(g_gatts_if, g_conn_id);
    }
    
    esp_ble_gap_stop_advertising();
    ESP_LOGI(TAG, "蓝牙通信停止");
    return ESP_OK;
}

/**
 * @brief 发送数据到主设备
 */
esp_err_t bluetooth_comm_send_data(uint8_t *data, uint16_t length)
{
    if (!g_bluetooth_initialized || !g_bluetooth_connected) {
        return ESP_ERR_INVALID_STATE;
    }
    
    if (length > 20) { // BLE最大数据包长度限制
        length = 20;
    }
    
    return esp_ble_gatts_send_indicate(g_gatts_if, g_conn_id, g_char_handle_data, length, data, false);
}

/**
 * @brief 获取蓝牙连接状态
 */
bool bluetooth_comm_is_connected(void)
{
    return g_bluetooth_connected;
}
