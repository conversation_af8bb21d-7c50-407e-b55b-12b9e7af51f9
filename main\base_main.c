/**
 * @file base_main.c
 * @brief TIMO底座设备主程序实现
 * @version 1.0.0
 * @date 2025-06-27
 */

#include "base_main.h"
#include "base_config.h"
#include "ble_service.h"
#include "system_test.h"
#include "esp_log.h"
#include "esp_timer.h"
#include "esp_system.h"
#include "esp_sleep.h"
#include "esp_heap_caps.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include <string.h>

static const char *TAG = "BASE_MAIN";

/* 全局变量 */
EventGroupHandle_t g_base_event_group = NULL;
QueueHandle_t g_led_command_queue = NULL;
// QueueHandle_t g_bluetooth_data_queue = NULL; // 暂时禁用蓝牙

/* 任务句柄 */
static TaskHandle_t h_led_task = NULL;
static TaskHandle_t h_sound_task = NULL;
static TaskHandle_t h_button_task = NULL;
// static TaskHandle_t h_bluetooth_task = NULL; // 暂时禁用蓝牙
static TaskHandle_t h_system_monitor_task = NULL;

/**
 * @brief BLE数据接收处理回调 - 暂时禁用
 */
/*
static void ble_data_received_handler(const uint8_t *data, uint16_t length)
{
    ESP_LOGI(TAG, "📱 收到BLE数据: 长度=%d", length);

    if (length > 0) {
        // 简单的命令解析示例
        switch (data[0]) {
        case 0x01: // LED控制命令
            if (length >= 4) {
                rgb_color_t color = {data[1], data[2], data[3]};
                ESP_LOGI(TAG, "💡 设置LED颜色: R=%d G=%d B=%d", color.r, color.g, color.b);
                ws2812_set_all_pixels(color);
                ws2812_refresh();
            }
            break;

        case 0x02: // 亮度控制命令
            if (length >= 2) {
                uint8_t brightness = data[1];
                ESP_LOGI(TAG, "🔆 设置亮度: %d", brightness);
                ws2812_set_brightness(brightness);
            }
            break;

        case 0x03: // 查询状态命令
            {
                uint8_t response[10];
                response[0] = 0x83; // 状态响应
                response[1] = ble_service_is_connected() ? 1 : 0;
                response[2] = sound_sensor_get_current_level() >> 8;
                response[3] = sound_sensor_get_current_level() & 0xFF;
                response[4] = button_handler_is_pressed() ? 1 : 0;
                ble_service_send_data(response, 5);
                ESP_LOGI(TAG, "📊 发送状态响应");
            }
            break;

        default:
            ESP_LOGW(TAG, "❓ 未知BLE命令: 0x%02X", data[0]);
            break;
        }
    }
}
*/

/* 系统状态 */
static base_system_status_t g_system_status = {0};

/* 外部函数声明 */
extern esp_err_t ws2812_init(void);
extern esp_err_t ws2812_start(void);
extern esp_err_t sound_sensor_init(void);
extern esp_err_t sound_sensor_start(void);
extern esp_err_t button_handler_init(void);
extern esp_err_t button_handler_start(void);
// extern esp_err_t bluetooth_comm_init(void); // 暂时禁用蓝牙
// extern esp_err_t bluetooth_comm_start(void); // 暂时禁用蓝牙
extern esp_err_t ambient_effects_init(void);
extern esp_err_t ambient_effects_start(void);

/**
 * @brief LED控制任务
 */
static void led_control_task(void *pvParameters)
{
    ESP_LOGI(TAG, "LED控制任务启动");
    
    led_command_t cmd;
    
    while (1) {
        if (xQueueReceive(g_led_command_queue, &cmd, pdMS_TO_TICKS(100)) == pdTRUE) {
            ESP_LOGI(TAG, "收到LED命令: scene=%d, brightness=%d", cmd.scene, cmd.brightness);
            
            // 更新系统状态
            g_system_status.current_scene = cmd.scene;
            g_system_status.brightness = cmd.brightness;
            
            // 这里会调用具体的LED控制函数
            // ambient_effects_set_scene(cmd.scene, cmd.color, cmd.brightness, cmd.speed);
        }
        
        // 定期更新LED状态
        vTaskDelay(pdMS_TO_TICKS(50));
    }
}

/**
 * @brief 声音监测任务
 */
static void sound_monitor_task(void *pvParameters)
{
    ESP_LOGI(TAG, "声音监测任务启动");
    
    while (1) {
        // 读取声音传感器数据
        // uint16_t sound_level = sound_sensor_read();
        // g_system_status.sound_level = sound_level;
        
        // 检测声音事件
        // if (sound_level > SOUND_THRESHOLD_HIGH) {
        //     sound_event_t event = {
        //         .level = sound_level,
        //         .timestamp = esp_timer_get_time() / 1000
        //     };
        //     
        //     // 发送声音事件
        //     xEventGroupSetBits(g_base_event_group, BASE_EVENT_SOUND_DETECTED);
        // }
        
        vTaskDelay(pdMS_TO_TICKS(SOUND_SAMPLE_RATE_MS));
    }
}

/**
 * @brief 按键处理任务
 */
static void button_handler_task(void *pvParameters)
{
    ESP_LOGI(TAG, "按键处理任务启动");
    
    while (1) {
        // 检测按键状态
        // button_event_t event;
        // if (button_handler_get_event(&event)) {
        //     g_system_status.user_button_pressed = event.pressed;
        //     
        //     if (event.pressed) {
        //         if (event.long_press) {
        //             xEventGroupSetBits(g_base_event_group, BASE_EVENT_BUTTON_LONG);
        //         } else {
        //             xEventGroupSetBits(g_base_event_group, BASE_EVENT_BUTTON_PRESS);
        //         }
        //     }
        // }
        
        vTaskDelay(pdMS_TO_TICKS(10));
    }
}

/**
 * @brief 蓝牙通信任务
 */
// 暂时禁用蓝牙通信任务
/*
static void bluetooth_comm_task(void *pvParameters)
{
    ESP_LOGI(TAG, "蓝牙通信任务启动");

    bluetooth_data_t data;

    while (1) {
        if (xQueueReceive(g_bluetooth_data_queue, &data, pdMS_TO_TICKS(100)) == pdTRUE) {
            ESP_LOGI(TAG, "收到蓝牙数据: type=%d, length=%d", data.type, data.length);

            // 处理蓝牙数据
            switch (data.type) {
                case 0x01: // LED控制命令
                    {
                        led_command_t cmd = {0};
                        if (data.length >= sizeof(led_command_t)) {
                            memcpy(&cmd, data.data, sizeof(led_command_t));
                            xQueueSend(g_led_command_queue, &cmd, 0);
                        }
                    }
                    break;

                case 0x02: // 系统状态查询
                    // 发送系统状态
                    break;

                default:
                    ESP_LOGW(TAG, "未知的蓝牙数据类型: %d", data.type);
                    break;
            }
        }

        vTaskDelay(pdMS_TO_TICKS(10));
    }
}
*/

/**
 * @brief 系统监控任务
 */
static void system_monitor_task(void *pvParameters)
{
    ESP_LOGI(TAG, "系统监控任务启动");
    
    while (1) {
        // 等待所有系统组件就绪
        EventBits_t bits = xEventGroupWaitBits(
            g_base_event_group,
            BASE_EVENT_SYSTEM_READY | BASE_EVENT_BLE_READY | 
            BASE_EVENT_LED_READY | BASE_EVENT_SOUND_READY | BASE_EVENT_BUTTON_READY,
            pdFALSE,
            pdTRUE,
            pdMS_TO_TICKS(1000)
        );
        
        if ((bits & (BASE_EVENT_SYSTEM_READY | BASE_EVENT_BLE_READY | 
                    BASE_EVENT_LED_READY | BASE_EVENT_SOUND_READY | BASE_EVENT_BUTTON_READY)) == 
            (BASE_EVENT_SYSTEM_READY | BASE_EVENT_BLE_READY | 
             BASE_EVENT_LED_READY | BASE_EVENT_SOUND_READY | BASE_EVENT_BUTTON_READY)) {
            ESP_LOGI(TAG, "所有系统组件已就绪");
            break;
        }
        
        ESP_LOGI(TAG, "等待系统组件就绪... (0x%08lx)", (unsigned long)bits);
    }
    
    // 系统监控循环
    uint32_t monitor_count = 0;
    while (1) {
        monitor_count++;

        // 监控内存使用情况
        size_t free_heap = esp_get_free_heap_size();
        size_t min_free_heap = esp_get_minimum_free_heap_size();

        // 计算内存使用率
        size_t total_heap = 272 * 1024; // ESP32-C2 总内存约272KB
        size_t used_heap = total_heap - free_heap;
        uint8_t memory_usage = (used_heap * 100) / total_heap;

        ESP_LOGI(TAG, "📊 内存状态 [%lu] - 空闲: %d bytes (%.1f%%), 最小空闲: %d bytes",
                 monitor_count, free_heap, (100.0 - memory_usage), min_free_heap);

        // 监控任务状态
        UBaseType_t task_count = uxTaskGetNumberOfTasks();
        ESP_LOGI(TAG, "📋 任务状态 - 当前任务数: %d", task_count);

        // 监控系统运行时间
        uint64_t uptime_ms = esp_timer_get_time() / 1000;
        uint32_t uptime_sec = uptime_ms / 1000;
        uint32_t hours = uptime_sec / 3600;
        uint32_t minutes = (uptime_sec % 3600) / 60;
        uint32_t seconds = uptime_sec % 60;

        ESP_LOGI(TAG, "⏱️  系统运行时间: %02lu:%02lu:%02lu", hours, minutes, seconds);

        // 内存警告检查和优化
        if (memory_usage > 85) {
            ESP_LOGW(TAG, "⚠️  内存使用率过高: %d%% - 触发内存优化", memory_usage);
            // 触发内存优化
            if (monitor_count % 10 == 0) { // 每10次监控执行一次深度优化
                ESP_LOGI(TAG, "🔧 执行内存优化...");
                // 强制垃圾回收
                heap_caps_check_integrity_all(true);
            }
        }

        if (free_heap < 8192) { // 小于8KB
            ESP_LOGW(TAG, "⚠️  可用内存不足: %d bytes - 进入节能模式", free_heap);
            // 可以在这里实现节能措施，比如降低任务频率
        }

        // 内存碎片检查
        size_t largest_free_block = heap_caps_get_largest_free_block(MALLOC_CAP_8BIT);
        if (largest_free_block < (free_heap / 2)) {
            ESP_LOGW(TAG, "⚠️  内存碎片化严重 - 最大连续块: %d bytes", largest_free_block);
        }

        // 延时30秒
        vTaskDelay(pdMS_TO_TICKS(30000));
    }
}

/**
 * @brief 启动底座应用程序
 */
esp_err_t base_main_start(void)
{
    ESP_LOGI(TAG, "启动TIMO底座设备应用程序");
    
    // 创建全局事件组
    g_base_event_group = xEventGroupCreate();
    if (g_base_event_group == NULL) {
        ESP_LOGE(TAG, "创建系统事件组失败");
        return ESP_ERR_NO_MEM;
    }
    
    // 创建消息队列
    g_led_command_queue = xQueueCreate(10, sizeof(led_command_t));
    // g_bluetooth_data_queue = xQueueCreate(5, sizeof(bluetooth_data_t)); // 暂时禁用蓝牙

    if (!g_led_command_queue) {
        ESP_LOGE(TAG, "创建消息队列失败");
        return ESP_ERR_NO_MEM;
    }
    
    // 初始化各个模块
    ESP_LOGI(TAG, "初始化WS2812 LED控制器...");
    esp_err_t ret = ws2812_init();
    if (ret == ESP_OK) {
        ret = ws2812_start();
        if (ret == ESP_OK) {
            xEventGroupSetBits(g_base_event_group, BASE_EVENT_LED_READY);
            ESP_LOGI(TAG, "✓ WS2812 LED控制器初始化成功");
        } else {
            ESP_LOGE(TAG, "✗ WS2812 LED控制器启动失败: %s", esp_err_to_name(ret));
        }
    } else {
        ESP_LOGE(TAG, "✗ WS2812 LED控制器初始化失败: %s", esp_err_to_name(ret));
    }
    
    ESP_LOGI(TAG, "初始化声音传感器...");
    ret = sound_sensor_init();
    if (ret == ESP_OK) {
        ret = sound_sensor_start();
        if (ret == ESP_OK) {
            xEventGroupSetBits(g_base_event_group, BASE_EVENT_SOUND_READY);
            ESP_LOGI(TAG, "✓ 声音传感器初始化成功");
        } else {
            ESP_LOGE(TAG, "✗ 声音传感器启动失败: %s", esp_err_to_name(ret));
        }
    } else {
        ESP_LOGE(TAG, "✗ 声音传感器初始化失败: %s", esp_err_to_name(ret));
    }
    
    ESP_LOGI(TAG, "初始化按键处理器...");
    ret = button_handler_init();
    if (ret == ESP_OK) {
        ret = button_handler_start();
        if (ret == ESP_OK) {
            xEventGroupSetBits(g_base_event_group, BASE_EVENT_BUTTON_READY);
            ESP_LOGI(TAG, "✓ 按键处理器初始化成功");
        } else {
            ESP_LOGE(TAG, "✗ 按键处理器启动失败: %s", esp_err_to_name(ret));
        }
    } else {
        ESP_LOGE(TAG, "✗ 按键处理器初始化失败: %s", esp_err_to_name(ret));
    }

    // 初始化BLE服务 - 暂时禁用
    /*
    ESP_LOGI(TAG, "初始化BLE服务...");
    ret = ble_service_init();
    if (ret == ESP_OK) {
        ble_service_set_data_received_callback(ble_data_received_handler);
        ret = ble_service_start();
        if (ret == ESP_OK) {
            xEventGroupSetBits(g_base_event_group, BASE_EVENT_BLUETOOTH_READY);
            ESP_LOGI(TAG, "✓ BLE服务初始化成功");
        } else {
            ESP_LOGE(TAG, "✗ BLE服务启动失败: %s", esp_err_to_name(ret));
        }
    } else {
        ESP_LOGE(TAG, "✗ BLE服务初始化失败: %s", esp_err_to_name(ret));
    }
    */
    // 暂时直接设置BLE就绪标志
    xEventGroupSetBits(g_base_event_group, BASE_EVENT_BLUETOOTH_READY);
    
    // 暂时禁用蓝牙通信
    /*
    ESP_LOGI(TAG, "初始化蓝牙通信...");
    ret = bluetooth_comm_init();
    if (ret == ESP_OK) {
        ret = bluetooth_comm_start();
        if (ret == ESP_OK) {
            xEventGroupSetBits(g_base_event_group, BASE_EVENT_BLE_READY);
        }
    }
    */
    // 直接设置蓝牙就绪标志
    xEventGroupSetBits(g_base_event_group, BASE_EVENT_BLE_READY);
    
    ESP_LOGI(TAG, "初始化氛围灯效...");
    ret = ambient_effects_init();
    if (ret == ESP_OK) {
        ret = ambient_effects_start();
    }
    
    // 创建任务
    ESP_LOGI(TAG, "创建系统任务...");

    BaseType_t task_ret;

    task_ret = xTaskCreate(led_control_task, "led_ctrl", TASK_STACK_SIZE_MEDIUM, NULL, TASK_PRIORITY_NORMAL, &h_led_task);
    if (task_ret != pdPASS) {
        ESP_LOGE(TAG, "✗ 创建LED控制任务失败");
        return ESP_ERR_NO_MEM;
    }
    ESP_LOGI(TAG, "✓ LED控制任务创建成功 (堆栈: %d bytes)", TASK_STACK_SIZE_MEDIUM);

    task_ret = xTaskCreate(sound_monitor_task, "sound_mon", TASK_STACK_SIZE_TINY, NULL, TASK_PRIORITY_LOW, &h_sound_task);
    if (task_ret != pdPASS) {
        ESP_LOGE(TAG, "✗ 创建声音监测任务失败");
        return ESP_ERR_NO_MEM;
    }
    ESP_LOGI(TAG, "✓ 声音监测任务创建成功 (堆栈: %d bytes)", TASK_STACK_SIZE_TINY);

    task_ret = xTaskCreate(button_handler_task, "button", TASK_STACK_SIZE_TINY, NULL, TASK_PRIORITY_NORMAL, &h_button_task);
    if (task_ret != pdPASS) {
        ESP_LOGE(TAG, "✗ 创建按键处理任务失败");
        return ESP_ERR_NO_MEM;
    }
    ESP_LOGI(TAG, "✓ 按键处理任务创建成功 (堆栈: %d bytes)", TASK_STACK_SIZE_TINY);

    // xTaskCreate(bluetooth_comm_task, "bt_comm", TASK_STACK_SIZE_MEDIUM, NULL, TASK_PRIORITY_NORMAL, &h_bluetooth_task); // 暂时禁用蓝牙

    task_ret = xTaskCreate(system_monitor_task, "sys_mon", TASK_STACK_SIZE_SMALL, NULL, TASK_PRIORITY_LOW, &h_system_monitor_task);
    if (task_ret != pdPASS) {
        ESP_LOGE(TAG, "✗ 创建系统监控任务失败");
        return ESP_ERR_NO_MEM;
    }
    ESP_LOGI(TAG, "✓ 系统监控任务创建成功 (堆栈: %d bytes)", TASK_STACK_SIZE_SMALL);
    
    xEventGroupSetBits(g_base_event_group, BASE_EVENT_SYSTEM_READY);

    ESP_LOGI(TAG, "🎉 TIMO底座设备启动完成！");

    // 启动后运行快速测试
    ESP_LOGI(TAG, "🧪 运行启动测试...");
    system_test_run_quick();

    return ESP_OK;
}

/**
 * @brief 停止底座应用程序
 */
esp_err_t base_main_stop(void)
{
    ESP_LOGI(TAG, "停止TIMO底座设备应用程序");

    // 停止各个模块
    ambient_effects_stop();
    bluetooth_comm_stop();
    sound_sensor_stop();
    button_handler_stop();

    // 清理资源
    if (g_base_event_group) {
        vEventGroupDelete(g_base_event_group);
        g_base_event_group = NULL;
    }

    if (g_led_command_queue) {
        vQueueDelete(g_led_command_queue);
        g_led_command_queue = NULL;
    }

    // 暂时禁用蓝牙队列
    /*
    if (g_bluetooth_data_queue) {
        vQueueDelete(g_bluetooth_data_queue);
        g_bluetooth_data_queue = NULL;
    }
    */

    ESP_LOGI(TAG, "底座设备应用程序停止完成");
    return ESP_OK;
}

/**
 * @brief 获取系统运行时间
 * @return uint64_t 运行时间(毫秒)
 */
uint64_t base_get_uptime_ms(void)
{
    return esp_timer_get_time() / 1000;
}

/**
 * @brief 系统重启
 */
void base_system_restart(void)
{
    ESP_LOGI(TAG, "系统重启...");
    esp_restart();
}

/**
 * @brief 进入深度睡眠
 * @param sleep_time_us 睡眠时间(微秒)
 */
void base_enter_deep_sleep(uint64_t sleep_time_us)
{
    ESP_LOGI(TAG, "进入深度睡眠模式，睡眠时间: %llu us", sleep_time_us);
    esp_sleep_enable_timer_wakeup(sleep_time_us);
    esp_deep_sleep_start();
}

/**
 * @brief 获取系统状态
 */
base_system_status_t* base_get_system_status(void)
{
    return &g_system_status;
}

/**
 * @brief 设置状态LED
 */
void base_set_status_led(bool on)
{
    gpio_set_level(STATUS_LED_GPIO, on ? 1 : 0);
}

/**
 * @brief 切换状态LED
 */
void base_toggle_status_led(void)
{
    static bool led_state = false;
    led_state = !led_state;
    base_set_status_led(led_state);
}
