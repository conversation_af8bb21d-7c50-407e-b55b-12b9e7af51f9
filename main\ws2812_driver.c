/**
 * @file ws2812_driver.c
 * @brief WS2812 RGB LED驱动实现
 * @version 1.0.0
 * @date 2025-06-27
 */

#include "base_config.h"
#include "esp_log.h"
#include "esp_err.h"
#include "driver/gpio.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include <string.h>

// ESP32-C2不支持RMT，使用GPIO模拟LED控制

static const char *TAG = "WS2812";

/* LED状态 */
static bool g_ws2812_initialized = false;
static bool g_led_state = false;

/* LED缓冲区 */
static rgb_color_t g_led_buffer[WS2812_LED_COUNT];
static uint8_t g_global_brightness = 100;

/**
 * @brief 初始化WS2812 LED驱动
 * @note ESP32-C2不支持RMT，使用GPIO模拟LED控制
 */
esp_err_t ws2812_init(void)
{
    if (g_ws2812_initialized) {
        ESP_LOGW(TAG, "WS2812已初始化");
        return ESP_OK;
    }

    ESP_LOGI(TAG, "初始化WS2812 LED驱动 (GPIO模拟模式)...");

    // 配置GPIO作为输出
    gpio_config_t io_conf = {
        .intr_type = GPIO_INTR_DISABLE,
        .mode = GPIO_MODE_OUTPUT,
        .pin_bit_mask = (1ULL << WS2812_GPIO),
        .pull_down_en = 0,
        .pull_up_en = 0,
    };

    esp_err_t ret = gpio_config(&io_conf);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "配置GPIO失败: %s", esp_err_to_name(ret));
        return ret;
    }

    // 初始化为低电平
    gpio_set_level(WS2812_GPIO, 0);
    g_led_state = false;

    // 初始化LED缓冲区
    memset(g_led_buffer, 0, sizeof(g_led_buffer));

    g_ws2812_initialized = true;
    ESP_LOGI(TAG, "WS2812 LED驱动初始化完成 (使用GPIO%d)", WS2812_GPIO);
    return ESP_OK;
}

/**
 * @brief 启动WS2812 LED驱动
 */
esp_err_t ws2812_start(void)
{
    if (!g_ws2812_initialized) {
        ESP_LOGE(TAG, "WS2812未初始化");
        return ESP_ERR_INVALID_STATE;
    }
    
    ESP_LOGI(TAG, "启动WS2812 LED驱动");
    return ESP_OK;
}

/**
 * @brief 设置单个LED颜色
 * @note ESP32-C2模拟模式：根据颜色亮度控制GPIO状态
 */
esp_err_t ws2812_set_pixel(uint16_t index, rgb_color_t color)
{
    if (!g_ws2812_initialized) {
        return ESP_ERR_INVALID_STATE;
    }

    if (index >= WS2812_LED_COUNT) {
        return ESP_ERR_INVALID_ARG;
    }

    // 更新缓冲区
    g_led_buffer[index] = color;

    // 简单的亮度检测：如果任何颜色分量大于0，则点亮LED
    bool should_light = (color.r > 0) || (color.g > 0) || (color.b > 0);

    if (should_light != g_led_state) {
        g_led_state = should_light;
        gpio_set_level(WS2812_GPIO, g_led_state ? 1 : 0);
    }

    return ESP_OK;
}

/**
 * @brief 设置所有LED颜色
 */
esp_err_t ws2812_set_all_pixels(rgb_color_t color)
{
    if (!g_ws2812_initialized) {
        return ESP_ERR_INVALID_STATE;
    }

    // 更新所有缓冲区
    for (uint16_t i = 0; i < WS2812_LED_COUNT; i++) {
        g_led_buffer[i] = color;
    }

    // 简单的亮度检测：如果任何颜色分量大于0，则点亮LED
    bool should_light = (color.r > 0) || (color.g > 0) || (color.b > 0);

    if (should_light != g_led_state) {
        g_led_state = should_light;
        gpio_set_level(WS2812_GPIO, g_led_state ? 1 : 0);
    }

    return ESP_OK;
}

/**
 * @brief 清空所有LED
 */
esp_err_t ws2812_clear_all(void)
{
    if (!g_ws2812_initialized) {
        return ESP_ERR_INVALID_STATE;
    }

    rgb_color_t black = COLOR_OFF;
    return ws2812_set_all_pixels(black);
}

/**
 * @brief 刷新LED显示
 * @note ESP32-C2模拟模式：无需刷新操作
 */
esp_err_t ws2812_refresh(void)
{
    if (!g_ws2812_initialized) {
        return ESP_ERR_INVALID_STATE;
    }

    // GPIO模式下无需刷新操作
    return ESP_OK;
}

/**
 * @brief 设置全局亮度
 */
esp_err_t ws2812_set_brightness(uint8_t brightness)
{
    // uint8_t类型范围0-255，无需检查上限
    g_global_brightness = brightness;

    // 重新应用当前颜色
    if (g_ws2812_initialized) {
        // 检查是否有任何LED应该点亮
        bool should_light = false;
        for (uint16_t i = 0; i < WS2812_LED_COUNT; i++) {
            if ((g_led_buffer[i].r > 0) || (g_led_buffer[i].g > 0) || (g_led_buffer[i].b > 0)) {
                should_light = true;
                break;
            }
        }

        if (should_light != g_led_state) {
            g_led_state = should_light;
            gpio_set_level(WS2812_GPIO, g_led_state ? 1 : 0);
        }
    }

    return ESP_OK;
}

/**
 * @brief 获取全局亮度
 */
uint8_t ws2812_get_brightness(void)
{
    return g_global_brightness;
}

/**
 * @brief 彩虹效果
 */
esp_err_t ws2812_rainbow_effect(uint8_t brightness, uint16_t speed)
{
    if (!g_ws2812_initialized) {
        return ESP_ERR_INVALID_STATE;
    }
    
    static uint8_t hue_offset = 0;
    
    for (uint16_t i = 0; i < WS2812_LED_COUNT; i++) {
        uint8_t hue = (i * 255 / WS2812_LED_COUNT + hue_offset) % 255;
        
        // HSV转RGB (简化版本)
        rgb_color_t color;
        if (hue < 85) {
            color.r = hue * 3;
            color.g = 255 - hue * 3;
            color.b = 0;
        } else if (hue < 170) {
            hue -= 85;
            color.r = 255 - hue * 3;
            color.g = 0;
            color.b = hue * 3;
        } else {
            hue -= 170;
            color.r = 0;
            color.g = hue * 3;
            color.b = 255 - hue * 3;
        }
        
        // 应用亮度
        color.r = (color.r * brightness) / 255;
        color.g = (color.g * brightness) / 255;
        color.b = (color.b * brightness) / 255;
        
        ws2812_set_pixel(i, color);
    }
    
    ws2812_refresh();
    
    hue_offset += speed;
    return ESP_OK;
}

/**
 * @brief 呼吸灯效果
 */
esp_err_t ws2812_breathing_effect(rgb_color_t color, uint16_t speed)
{
    if (!g_ws2812_initialized) {
        return ESP_ERR_INVALID_STATE;
    }
    
    static uint8_t brightness = 0;
    static int8_t direction = 1;
    
    // 计算亮度
    brightness += direction * speed;
    if (brightness >= 255) {
        brightness = 255;
        direction = -1;
    } else if (brightness <= 0) {
        brightness = 0;
        direction = 1;
    }
    
    // 应用亮度到颜色
    rgb_color_t dimmed_color = {
        .r = (color.r * brightness) / 255,
        .g = (color.g * brightness) / 255,
        .b = (color.b * brightness) / 255
    };
    
    ws2812_set_all_pixels(dimmed_color);
    return ws2812_refresh();
}

/**
 * @brief 波浪效果
 */
esp_err_t ws2812_wave_effect(rgb_color_t color, uint8_t brightness, uint16_t speed)
{
    if (!g_ws2812_initialized) {
        return ESP_ERR_INVALID_STATE;
    }
    
    static uint16_t wave_offset = 0;
    
    for (uint16_t i = 0; i < WS2812_LED_COUNT; i++) {
        // 计算波浪强度 (简化的正弦波)
        uint16_t wave_pos = (i * 255 / WS2812_LED_COUNT + wave_offset) % 255;
        uint8_t intensity;
        
        if (wave_pos < 128) {
            intensity = wave_pos * 2;
        } else {
            intensity = (255 - wave_pos) * 2;
        }
        
        // 应用强度到颜色
        rgb_color_t wave_color = {
            .r = (color.r * intensity * brightness) / (255 * 255),
            .g = (color.g * intensity * brightness) / (255 * 255),
            .b = (color.b * intensity * brightness) / (255 * 255)
        };
        
        ws2812_set_pixel(i, wave_color);
    }
    
    ws2812_refresh();
    
    wave_offset += speed;
    return ESP_OK;
}
