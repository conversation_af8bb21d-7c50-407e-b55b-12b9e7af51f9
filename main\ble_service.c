/**
 * @file ble_service.c
 * @brief TIMO底座BLE服务实现
 * @version 1.0.0
 * @date 2025-06-27
 */

#include "ble_service.h"
#include "base_config.h"
#include "esp_log.h"
#include "esp_nimble_hci.h"
#include "nimble/nimble_port.h"
#include "nimble/nimble_port_freertos.h"
#include "host/ble_hs.h"
#include "host/util/util.h"
#include "services/gap/ble_svc_gap.h"
#include "services/gatt/ble_svc_gatt.h"
#include <string.h>

static const char *TAG = "BLE_SERVICE";

/* BLE服务状态 */
static bool g_ble_initialized = false;
static bool g_ble_connected = false;
static uint16_t g_conn_handle = BLE_HS_CONN_HANDLE_NONE;

/* 服务和特征UUID */
static const ble_uuid128_t g_service_uuid = 
    BLE_UUID128_INIT(0x12, 0x34, 0x56, 0x78, 0x12, 0x34, 0x12, 0x34,
                     0x12, 0x34, 0x12, 0x34, 0x56, 0x78, 0x9a, 0xbc);

static const ble_uuid128_t g_char_tx_uuid = 
    BLE_UUID128_INIT(0x87, 0x65, 0x43, 0x21, 0x43, 0x21, 0x43, 0x21,
                     0x43, 0x21, 0xcb, 0xa9, 0x87, 0x65, 0x43, 0x21);

static const ble_uuid128_t g_char_rx_uuid = 
    BLE_UUID128_INIT(0x11, 0x11, 0x11, 0x11, 0x22, 0x22, 0x33, 0x33,
                     0x44, 0x44, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55);

/* 特征值句柄 */
static uint16_t g_char_tx_handle;
static uint16_t g_char_rx_handle;

/* 接收数据回调 */
static ble_data_received_cb_t g_data_received_cb = NULL;

/**
 * @brief GAP事件处理
 */
static int ble_gap_event_handler(struct ble_gap_event *event, void *arg)
{
    switch (event->type) {
    case BLE_GAP_EVENT_CONNECT:
        ESP_LOGI(TAG, "BLE连接事件: %s", 
                 event->connect.status == 0 ? "成功" : "失败");
        if (event->connect.status == 0) {
            g_ble_connected = true;
            g_conn_handle = event->connect.conn_handle;
        }
        break;

    case BLE_GAP_EVENT_DISCONNECT:
        ESP_LOGI(TAG, "BLE断开连接: 原因=%d", event->disconnect.reason);
        g_ble_connected = false;
        g_conn_handle = BLE_HS_CONN_HANDLE_NONE;
        
        /* 重新开始广播 */
        ble_service_start_advertising();
        break;

    case BLE_GAP_EVENT_ADV_COMPLETE:
        ESP_LOGI(TAG, "广播完成");
        ble_service_start_advertising();
        break;

    default:
        break;
    }

    return 0;
}

/**
 * @brief GATT特征访问处理
 */
static int ble_gatt_access_handler(uint16_t conn_handle, uint16_t attr_handle,
                                   struct ble_gatt_access_ctxt *ctxt, void *arg)
{
    switch (ctxt->op) {
    case BLE_GATT_ACCESS_OP_READ_CHR:
        ESP_LOGI(TAG, "特征读取请求");
        break;

    case BLE_GATT_ACCESS_OP_WRITE_CHR:
        ESP_LOGI(TAG, "特征写入请求: 长度=%d", ctxt->om->om_len);
        
        if (g_data_received_cb && ctxt->om->om_len > 0) {
            uint8_t data[256];
            uint16_t len = min(ctxt->om->om_len, sizeof(data));
            ble_hs_mbuf_to_flat(ctxt->om, data, len, NULL);
            g_data_received_cb(data, len);
        }
        break;

    default:
        ESP_LOGW(TAG, "未知GATT操作: %d", ctxt->op);
        return BLE_ATT_ERR_UNLIKELY;
    }

    return 0;
}

/* GATT服务定义 */
static const struct ble_gatt_svc_def g_gatt_services[] = {
    {
        .type = BLE_GATT_SVC_TYPE_PRIMARY,
        .uuid = &g_service_uuid.u,
        .characteristics = (struct ble_gatt_chr_def[]) {
            {
                /* TX特征 (通知) */
                .uuid = &g_char_tx_uuid.u,
                .access_cb = ble_gatt_access_handler,
                .flags = BLE_GATT_CHR_F_NOTIFY,
                .val_handle = &g_char_tx_handle,
            },
            {
                /* RX特征 (写入) */
                .uuid = &g_char_rx_uuid.u,
                .access_cb = ble_gatt_access_handler,
                .flags = BLE_GATT_CHR_F_WRITE | BLE_GATT_CHR_F_WRITE_NO_RSP,
                .val_handle = &g_char_rx_handle,
            },
            {
                0, /* 结束标记 */
            }
        },
    },
    {
        0, /* 结束标记 */
    },
};

/**
 * @brief 开始BLE广播
 */
esp_err_t ble_service_start_advertising(void)
{
    if (!g_ble_initialized) {
        return ESP_ERR_INVALID_STATE;
    }

    struct ble_gap_adv_params adv_params;
    struct ble_hs_adv_fields fields;
    int rc;

    /* 设置广播参数 */
    memset(&adv_params, 0, sizeof(adv_params));
    adv_params.conn_mode = BLE_GAP_CONN_MODE_UND;
    adv_params.disc_mode = BLE_GAP_DISC_MODE_GEN;

    /* 设置广播数据 */
    memset(&fields, 0, sizeof(fields));
    fields.flags = BLE_HS_ADV_F_DISC_GEN | BLE_HS_ADV_F_BREDR_UNSUP;
    fields.name = (uint8_t *)BT_DEVICE_NAME;
    fields.name_len = strlen(BT_DEVICE_NAME);
    fields.name_is_complete = 1;

    rc = ble_gap_adv_set_fields(&fields);
    if (rc != 0) {
        ESP_LOGE(TAG, "设置广播数据失败: %d", rc);
        return ESP_FAIL;
    }

    /* 开始广播 */
    rc = ble_gap_adv_start(BLE_OWN_ADDR_PUBLIC, NULL, BLE_HS_FOREVER,
                           &adv_params, ble_gap_event_handler, NULL);
    if (rc != 0) {
        ESP_LOGE(TAG, "开始广播失败: %d", rc);
        return ESP_FAIL;
    }

    ESP_LOGI(TAG, "BLE广播已开始");
    return ESP_OK;
}

/**
 * @brief BLE主机任务同步回调
 */
static void ble_on_sync(void)
{
    int rc;

    /* 确保我们有一个身份 */
    rc = ble_hs_util_ensure_addr(0);
    assert(rc == 0);

    /* 开始广播 */
    ble_service_start_advertising();
}

/**
 * @brief BLE主机重置回调
 */
static void ble_on_reset(int reason)
{
    ESP_LOGE(TAG, "BLE主机重置: 原因=%d", reason);
}

/**
 * @brief 初始化BLE服务
 */
esp_err_t ble_service_init(void)
{
    if (g_ble_initialized) {
        ESP_LOGW(TAG, "BLE服务已初始化");
        return ESP_OK;
    }

    ESP_LOGI(TAG, "初始化BLE服务...");

    /* 初始化NimBLE HCI */
    esp_err_t ret = esp_nimble_hci_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "NimBLE HCI初始化失败: %s", esp_err_to_name(ret));
        return ret;
    }

    /* 初始化NimBLE端口 */
    nimble_port_init();

    /* 设置主机配置 */
    ble_hs_cfg.sync_cb = ble_on_sync;
    ble_hs_cfg.reset_cb = ble_on_reset;

    /* 初始化GATT服务 */
    int rc = ble_gatts_count_cfg(g_gatt_services);
    if (rc != 0) {
        ESP_LOGE(TAG, "GATT服务计数失败: %d", rc);
        return ESP_FAIL;
    }

    rc = ble_gatts_add_svcs(g_gatt_services);
    if (rc != 0) {
        ESP_LOGE(TAG, "添加GATT服务失败: %d", rc);
        return ESP_FAIL;
    }

    /* 设置设备名称 */
    rc = ble_svc_gap_device_name_set(BT_DEVICE_NAME);
    if (rc != 0) {
        ESP_LOGE(TAG, "设置设备名称失败: %d", rc);
        return ESP_FAIL;
    }

    g_ble_initialized = true;
    ESP_LOGI(TAG, "BLE服务初始化完成");
    return ESP_OK;
}

/**
 * @brief 启动BLE服务
 */
esp_err_t ble_service_start(void)
{
    if (!g_ble_initialized) {
        return ESP_ERR_INVALID_STATE;
    }

    ESP_LOGI(TAG, "启动BLE服务...");

    /* 启动NimBLE主机任务 */
    nimble_port_freertos_init(NULL);

    ESP_LOGI(TAG, "BLE服务启动完成");
    return ESP_OK;
}

/**
 * @brief 发送数据
 */
esp_err_t ble_service_send_data(const uint8_t *data, uint16_t length)
{
    if (!g_ble_connected) {
        return ESP_ERR_INVALID_STATE;
    }

    struct os_mbuf *om = ble_hs_mbuf_from_flat(data, length);
    if (om == NULL) {
        return ESP_ERR_NO_MEM;
    }

    int rc = ble_gatts_notify_custom(g_conn_handle, g_char_tx_handle, om);
    if (rc != 0) {
        ESP_LOGE(TAG, "发送通知失败: %d", rc);
        return ESP_FAIL;
    }

    return ESP_OK;
}

/**
 * @brief 设置数据接收回调
 */
void ble_service_set_data_received_callback(ble_data_received_cb_t callback)
{
    g_data_received_cb = callback;
}

/**
 * @brief 获取连接状态
 */
bool ble_service_is_connected(void)
{
    return g_ble_connected;
}

/**
 * @brief 停止BLE服务
 */
esp_err_t ble_service_stop(void)
{
    if (!g_ble_initialized) {
        return ESP_ERR_INVALID_STATE;
    }

    ESP_LOGI(TAG, "停止BLE服务...");

    /* 停止广播 */
    ble_gap_adv_stop();

    /* 断开连接 */
    if (g_ble_connected) {
        ble_gap_terminate(g_conn_handle, BLE_ERR_REM_USER_CONN_TERM);
    }

    /* 去初始化NimBLE */
    nimble_port_deinit();
    esp_nimble_hci_deinit();

    g_ble_initialized = false;
    g_ble_connected = false;
    g_conn_handle = BLE_HS_CONN_HANDLE_NONE;

    ESP_LOGI(TAG, "BLE服务已停止");
    return ESP_OK;
}
