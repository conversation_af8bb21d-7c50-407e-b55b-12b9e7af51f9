# ESP32-C2 TIMO底座设备配置文件

# 目标芯片配置
CONFIG_IDF_TARGET="esp32c2"
CONFIG_IDF_TARGET_ESP32C2=y

# 蓝牙配置 - 使用NimBLE
CONFIG_BT_ENABLED=y
CONFIG_BT_CONTROLLER_ENABLED=y
CONFIG_BT_CONTROLLER_ONLY=n
CONFIG_BT_HOST=y
CONFIG_BT_NIMBLE_ENABLED=y
CONFIG_BT_BLUEDROID_ENABLED=n

# NimBLE配置
CONFIG_BT_NIMBLE_MAX_CONNECTIONS=1
CONFIG_BT_NIMBLE_MAX_BONDS=3
CONFIG_BT_NIMBLE_MAX_CCCDS=8
CONFIG_BT_NIMBLE_L2CAP_COC_MAX_NUM=0
CONFIG_BT_NIMBLE_PINNED_TO_CORE_0=y
CONFIG_BT_NIMBLE_TASK_STACK_SIZE=4096

# 内存优化
CONFIG_ESP_MAIN_TASK_STACK_SIZE=3072
CONFIG_ESP_MINIMAL_SHARED_STACK_SIZE=2048
CONFIG_FREERTOS_IDLE_TASK_STACKSIZE=1024
CONFIG_FREERTOS_ISR_STACKSIZE=1536

# 日志配置
CONFIG_LOG_DEFAULT_LEVEL_INFO=y
CONFIG_LOG_DEFAULT_LEVEL=3
CONFIG_LOG_MAXIMUM_EQUALS_DEFAULT=y
CONFIG_LOG_COLORS=y
CONFIG_LOG_TIMESTAMP_SOURCE_RTOS=y

# 电源管理
CONFIG_PM_ENABLE=y
CONFIG_PM_DFS_INIT_AUTO=y
CONFIG_PM_USE_RTC_TIMER_REF=y

# WiFi禁用（节省内存）
CONFIG_ESP_WIFI_ENABLED=n

# 看门狗配置
CONFIG_ESP_TASK_WDT_EN=y
CONFIG_ESP_TASK_WDT_TIMEOUT_S=10
CONFIG_ESP_TASK_WDT_CHECK_IDLE_TASK_CPU0=y

# SPI配置
CONFIG_SPI_MASTER_IN_IRAM=y
CONFIG_SPI_MASTER_ISR_IN_IRAM=y

# 编译器优化
CONFIG_COMPILER_OPTIMIZATION_SIZE=y
CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_SILENT=y

# 分区表
CONFIG_PARTITION_TABLE_SINGLE_APP=y
CONFIG_PARTITION_TABLE_CUSTOM_FILENAME="partitions.csv"

# 启动时间优化
CONFIG_BOOTLOADER_SKIP_VALIDATE_IN_DEEP_SLEEP=y
CONFIG_BOOTLOADER_RESERVE_RTC_SIZE=0

# 堆内存配置
CONFIG_HEAP_POISONING_DISABLED=y
CONFIG_HEAP_TRACING_OFF=y

# 系统事件配置
CONFIG_ESP_SYSTEM_EVENT_QUEUE_SIZE=16
CONFIG_ESP_SYSTEM_EVENT_TASK_STACK_SIZE=2048

# ADC配置
CONFIG_ADC_ONESHOT_CTRL_FUNC_IN_IRAM=y

# GPIO配置
CONFIG_GPIO_ESP32_SUPPORT_SWITCH_SLP_PULL=y
