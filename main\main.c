/**
 * @file main.c
 * @brief TIMO智能闹钟底座设备主程序 (ESP32-C2)
 * @version 1.0.0
 * @date 2025-06-27
 * 
 * 主要功能：
 * - WS2812 RGB灯效控制
 * - 声音传感器监测
 * - 用户按键处理
 * - 无线充电管理
 * - 蓝牙通信
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "freertos/queue.h"
#include "esp_system.h"
// #include "esp_wifi.h" // ESP32-C2项目暂不使用WiFi
#include "esp_event.h"
#include "esp_log.h"
#include "nvs_flash.h"
// #include "esp_netif.h" // ESP32-C2项目暂不使用网络
#include "esp_err.h"
#include "esp_timer.h"
// #include "esp_sleep.h" // ESP32-C2 sleep功能有限
#include "esp_pm.h"
// ESP32-C2 蓝牙头文件在bluetooth_comm.c中处理

#include "base_config.h"
#include "base_main.h"

static const char *TAG = "BASE_MAIN";

/**
 * @brief 系统初始化
 */
static void system_init(void)
{
    ESP_LOGI(TAG, "=== TIMO智能闹钟底座设备启动 ===");
    ESP_LOGI(TAG, "版本: %s", "1.0.0");
    ESP_LOGI(TAG, "编译时间: %s %s", __DATE__, __TIME__);
    ESP_LOGI(TAG, "芯片型号: ESP32-C2");
    
    // 初始化NVS
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);
    ESP_LOGI(TAG, "NVS初始化完成");
    
    // 初始化事件循环（不使用网络接口）
    ESP_ERROR_CHECK(esp_event_loop_create_default());
    ESP_LOGI(TAG, "事件循环初始化完成");
    
    // 暂时禁用蓝牙控制器初始化
    /*
    esp_bt_controller_config_t bt_cfg = BT_CONTROLLER_INIT_CONFIG_DEFAULT();
    ret = esp_bt_controller_init(&bt_cfg);
    if (ret) {
        ESP_LOGE(TAG, "蓝牙控制器初始化失败: %s", esp_err_to_name(ret));
        return;
    }

    ret = esp_bt_controller_enable(ESP_BT_MODE_BLE);
    if (ret) {
        ESP_LOGE(TAG, "蓝牙控制器启用失败: %s", esp_err_to_name(ret));
        return;
    }
    */

    // 暂时禁用蓝牙协议栈
    /*
    ret = esp_bluedroid_init();
    if (ret) {
        ESP_LOGE(TAG, "蓝牙协议栈初始化失败: %s", esp_err_to_name(ret));
        return;
    }

    ret = esp_bluedroid_enable();
    if (ret) {
        ESP_LOGE(TAG, "蓝牙协议栈启用失败: %s", esp_err_to_name(ret));
        return;
    }

    ESP_LOGI(TAG, "蓝牙初始化完成");
    */
    
    // 初始化电源管理
    esp_pm_config_t pm_config = {
        .max_freq_mhz = 120,
        .min_freq_mhz = 40,
        .light_sleep_enable = true
    };
    ESP_ERROR_CHECK(esp_pm_configure(&pm_config));
    ESP_LOGI(TAG, "电源管理初始化完成");
}

/**
 * @brief 主程序入口
 */
void app_main(void)
{
    // 系统初始化
    system_init();
    
    // 启动底座应用程序
    base_main_start();
    
    ESP_LOGI(TAG, "底座设备启动完成，进入主循环");
    
    // 主循环 - 系统监控
    while (1) {
        // 打印系统状态
        ESP_LOGI(TAG, "底座设备运行中... 空闲堆内存: %lu bytes", (unsigned long)esp_get_free_heap_size());
        
        // 延时10秒
        vTaskDelay(pdMS_TO_TICKS(10000));
    }
}
