@echo off
echo ESP32-C2 TIMO Base Station Build Script
echo =====================================

echo Checking ESP-IDF environment...
if not exist "D:\APP\Espressif\esp\v5.3.3\esp-idf" (
    echo ERROR: ESP-IDF not found at expected location
    pause
    exit /b 1
)

echo Setting environment variables...
set "IDF_PATH=D:\APP\Espressif\esp\v5.3.3\esp-idf"
set "IDF_TOOLS_PATH=D:\APP\Espressif\.espressif\v5.3.3"

echo Cleaning previous build...
if exist build rmdir /s /q build

echo Starting build process...
echo Please use VSCode ESP-IDF extension to build the project
echo Or run: idf.py build

echo Build script completed.
pause
