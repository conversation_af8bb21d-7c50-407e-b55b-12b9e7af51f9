@echo off
echo ESP32-C2 TIMO Base Station Build Script
echo =====================================

echo Setting ESP-IDF environment...
set "IDF_PATH=D:\APP\Espressif\esp\v5.3.3\esp-idf"
set "PATH=D:\APP\Espressif\.espressif\v5.3.3\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin;%PATH%"
set "PATH=D:\APP\Espressif\.espressif\v5.3.3\tools\ninja\1.12.1;%PATH%"
set "PATH=D:\APP\Espressif\.espressif\v5.3.3\tools\cmake\3.30.2\bin;%PATH%"
set "PATH=D:\APP\Espressif\.espressif\v5.3.3\python_env\idf5.3_py3.11_env\Scripts;%PATH%"

echo Checking if tools are available...
where python >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found in PATH
    pause
    exit /b 1
)

echo Cleaning previous build...
if exist build rmdir /s /q build

echo Starting build...
echo Using Python from: D:\APP\Espressif\.espressif\v5.3.3\python_env\idf5.3_py3.11_env\Scripts\python.exe
"D:\APP\Espressif\.espressif\v5.3.3\python_env\idf5.3_py3.11_env\Scripts\python.exe" "%IDF_PATH%\tools\idf.py" set-target esp32c2
"D:\APP\Espressif\.espressif\v5.3.3\python_env\idf5.3_py3.11_env\Scripts\python.exe" "%IDF_PATH%\tools\idf.py" build

if errorlevel 1 (
    echo BUILD FAILED!
    pause
    exit /b 1
) else (
    echo BUILD SUCCESSFUL!
)

pause
